import { DeviceEventEmitter, Platform } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";

export interface IHealthDevice {
   mac: string;
   name?: string;
   type: string;
   rssi?: number;
}

// Legacy interface for compatibility with useIHealth hook
export interface DeviceInfo {
   mac: string;
   deviceName?: string;
   deviceType?: string;
   protocolString?: string;
   name?: string;
   type?: string;
}

export interface PulseOximeterReading {
   spo2: number;
   heartRate: number;
   timestamp: Date;
   deviceMac: string;
}

export interface BodyScaleReading {
   weight: number;
   timestamp: Date;
   deviceMac: string;
}

export interface IHealthMeasurement {
   deviceMac: string;
   timestamp: Date;
   data: any;
}

export type DeviceConnectionStatus =
   | "disconnected"
   | "connecting"
   | "connected"
   | "error";

type ConnectionStatus = "disconnected" | "connecting" | "connected" | "error";

export class IHealthService {
   private static instance: IHealthService;

   private discoveryTimeout: any = null;
   private connectedDevice: IHealthDevice | null = null;
   private connectionStatus: ConnectionStatus = "disconnected";
   private listeners: Array<(event: string, data: any) => void> = [];
   private scanListener: any = null;
   private scanFinishListener: any = null;
   private connectListener: any = null;
   private disconnectListener: any = null;
   private authListener: any = null;
   private isScanning: boolean = false;
   private isAuthenticated: boolean = false;

   private constructor() {
      this.setupEventListeners();
   }

   static getInstance(): IHealthService {
      if (!IHealthService.instance) {
         IHealthService.instance = new IHealthService();
      }
      return IHealthService.instance;
   }

   /**
    * Authenticate with iHealth SDK using license file
    * Based on working example from current iHealth-rn-sdk repository
    */
   async authenticate(): Promise<boolean> {
      return new Promise((resolve) => {
         try {
            console.log("🔐 Authenticating with iHealth SDK...");

            if (!iHealthDeviceManagerModule) {
               console.error("❌ iHealth module not available");
               resolve(false);
               return;
            }

            // Setup authentication listener based on working example
            this.authListener = DeviceEventEmitter.addListener(
               iHealthDeviceManagerModule.Event_Authenticate_Result,
               (event: any) => {
                  console.log(
                     "🔑 Authentication result:",
                     JSON.stringify(event),
                  );
                  this.isAuthenticated =
                     event.result === true || event.result === "true";
                  if (this.isAuthenticated) {
                     console.log("✅ Authentication successful!");
                  } else {
                     console.error("❌ Authentication failed:", event);
                  }
                  resolve(this.isAuthenticated);
               },
            );

            // According to working example, license file should be named "license.pem"
            const filename = "license.pem";
            console.log(`🔑 Authenticating with license file: ${filename}`);
            console.log("📋 Make sure license.pem is in your project:");
            console.log("  - iOS: Added to Xcode project bundle");
            console.log("  - Android: In android/app/src/main/assets/");

            // Call authentication method as per working example
            iHealthDeviceManagerModule.sdkAuthWithLicense(filename);
         } catch (error) {
            console.error("❌ Authentication failed:", error);
            console.error("💡 Please ensure:");
            console.error(
               "  1. You have a valid license.pem file from dev.ihealthlabs.com",
            );
            console.error("  2. The file is properly added to your project");
            console.error("  3. Your app bundle ID matches the license");
            resolve(false);
         }
      });
   }

   /**
    * Setup event listeners for device discovery and connection
    */
   private setupEventListeners(): void {
      try {
         console.log("🎧 Setting up iHealth event listeners...");

         // Device discovery events - using exact event names from working example
         this.scanListener = DeviceEventEmitter.addListener(
            iHealthDeviceManagerModule.Event_Scan_Device,
            (event: any) => {
               console.log("📱 Device discovered:", JSON.stringify(event));
               this.isScanning = true;
               // Convert to our interface format
               const device: IHealthDevice = {
                  mac: event.mac,
                  name: event.name,
                  type: event.type,
                  rssi: event.rssi,
               };
               this.notifyListeners("deviceDiscovered", device);
            },
         );

         this.scanFinishListener = DeviceEventEmitter.addListener(
            iHealthDeviceManagerModule.Event_Scan_Finish,
            () => {
               console.log("🔍 Device scan finished");
               this.isScanning = false;
               this.notifyListeners("scanFinished", {});
            },
         );

         // Connection events - using exact event names from working example
         this.connectListener = DeviceEventEmitter.addListener(
            iHealthDeviceManagerModule.Event_Device_Connected,
            (event: any) => {
               console.log("🔗 Device connected:", JSON.stringify(event));
               this.connectionStatus = "connected";
               this.connectedDevice = {
                  mac: event.mac,
                  name: event.name,
                  type: event.type,
               };
               this.notifyListeners("deviceConnected", event);
            },
         );

         this.disconnectListener = DeviceEventEmitter.addListener(
            iHealthDeviceManagerModule.Event_Device_Disconnect,
            (event: any) => {
               console.log("🔌 Device disconnected:", JSON.stringify(event));
               this.connectionStatus = "disconnected";
               this.connectedDevice = null;
               this.notifyListeners("deviceDisconnected", event);
            },
         );

         console.log("✅ Event listeners setup complete");
      } catch (error) {
         console.error("❌ Failed to setup event listeners:", error);
      }
   }

   /**
    * Start scanning for iHealth devices
    * Based on working example from current iHealth-rn-sdk repository
    * Supports both PO3M pulse oximeter and HS2S Pro scale
    */
   async startScan(deviceType?: string): Promise<void> {
      try {
         if (!iHealthDeviceManagerModule) {
            throw new Error("iHealth module not available");
         }

         // Check authentication first
         if (!this.isAuthenticated) {
            console.log("🔐 Authentication required before scanning");
            const authResult = await this.authenticate();
            if (!authResult) {
               throw new Error("Authentication failed - cannot start scanning");
            }
         }

         // Clear any existing timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         // Determine device type to scan for
         // Based on community findings:
         // - PO3M pulse oximeter uses device type "PO3"
         // - HS2S Pro scale uses device type "HS2S" (not "HS2S Pro")
         const scanType = deviceType || "PO3"; // Default to PO3 for pulse oximeter

         console.log("🔍 Starting iHealth device discovery...");
         if (scanType === "PO3") {
            console.log(
               "📱 Looking for PO3M pulse oximeter (device type: PO3)",
            );
            console.log(
               "🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!",
            );
            console.log("🫁 TURN ON YOUR PO3M PULSE OXIMETER NOW!");
         } else if (scanType === "HS2S") {
            console.log("📱 Looking for HS2S Pro scale (device type: HS2S)");
            console.log(
               "🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!",
            );
            console.log("⚖️ TURN ON YOUR HS2S PRO SCALE NOW!");
         }

         // Stop any existing discovery first
         try {
            if (iHealthDeviceManagerModule.stopDiscovery) {
               iHealthDeviceManagerModule.stopDiscovery();
               console.log("🛑 Previous discovery stopped");
            }
         } catch {
            console.log("ℹ️ No previous discovery to stop");
         }

         console.log(`📡 Starting discovery for device type: ${scanType}`);

         // Start discovery using the working example pattern
         this.isScanning = true;
         iHealthDeviceManagerModule.startDiscovery(scanType);
         console.log("✅ Discovery started successfully");
         console.log("⏳ Please wait for device discovery...");

         // Set timeout for discovery
         this.discoveryTimeout = setTimeout(() => {
            console.log("⏰ Discovery timeout reached - stopping scan");
            this.stopScan();
         }, 30000); // 30 seconds timeout
      } catch (error) {
         console.error("❌ Failed to start device discovery:", error);
         this.connectionStatus = "error";
         this.isScanning = false;
         throw error;
      }
   }

   /**
    * Stop device discovery
    */
   async stopScan(): Promise<void> {
      try {
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         if (iHealthDeviceManagerModule?.stopDiscovery) {
            iHealthDeviceManagerModule.stopDiscovery();
            console.log("🛑 Device discovery stopped");
         }

         this.isScanning = false;
      } catch (error) {
         console.error("❌ Failed to stop discovery:", error);
         this.isScanning = false;
      }
   }

   /**
    * Connect to a discovered device (returns boolean for hook compatibility)
    */
   async connectDevice(
      mac: string,
      deviceType: string = "PO3",
   ): Promise<boolean> {
      try {
         console.log(`🔗 Connecting to device: ${mac} (${deviceType})`);
         this.connectionStatus = "connecting";

         if (iHealthDeviceManagerModule?.connectDevice) {
            iHealthDeviceManagerModule.connectDevice(mac, deviceType);
            console.log("🔗 Connection initiated");
            return true;
         } else {
            throw new Error("connectDevice method not available");
         }
      } catch (error) {
         console.error("❌ Failed to connect device:", error);
         this.connectionStatus = "error";
         return false;
      }
   }

   /**
    * Disconnect from current device
    */
   async disconnectDevice(): Promise<void> {
      try {
         if (
            this.connectedDevice &&
            iHealthDeviceManagerModule?.disconnectDevice
         ) {
            iHealthDeviceManagerModule.disconnectDevice(
               this.connectedDevice.mac,
               this.connectedDevice.type,
            );
            console.log("🔌 Device disconnection initiated");
         }
      } catch (error) {
         console.error("❌ Failed to disconnect device:", error);
      }
   }

   /**
    * Check if iHealth SDK is available
    */
   isSDKAvailable(): boolean {
      try {
         return (
            !!iHealthDeviceManagerModule &&
            typeof iHealthDeviceManagerModule.startDiscovery === "function"
         );
      } catch (error) {
         console.error("❌ Error checking SDK availability:", error);
         return false;
      }
   }

   /**
    * Add event listener (legacy method for compatibility)
    */
   addEventListener(event: string, callback: (data: any) => void): void {
      this.addListener((eventType: string, data: any) => {
         if (eventType === event) {
            callback(data);
         }
      });
   }

   /**
    * Remove event listener (legacy method for compatibility)
    */
   removeEventListener(event: string, callback: (data: any) => void): void {
      // For simplicity, we'll just log this - the cleanup method handles all listeners
      console.log(`🗑️ Removing event listener for: ${event}`);
   }

   /**
    * Scan for PO3M pulse oximeter specifically
    */
   async scanForPulseOximeter(): Promise<void> {
      return this.startScan("PO3");
   }

   /**
    * Scan for HS2S Pro scale specifically
    */
   async scanForScale(): Promise<void> {
      return this.startScan("HS2S");
   }

   /**
    * Clean up all listeners and resources
    */
   cleanup(): void {
      try {
         console.log("🧹 Cleaning up iHealth service...");

         // Remove all event listeners
         if (this.scanListener) {
            this.scanListener.remove();
            this.scanListener = null;
         }

         if (this.scanFinishListener) {
            this.scanFinishListener.remove();
            this.scanFinishListener = null;
         }

         if (this.connectListener) {
            this.connectListener.remove();
            this.connectListener = null;
         }

         if (this.disconnectListener) {
            this.disconnectListener.remove();
            this.disconnectListener = null;
         }

         if (this.authListener) {
            this.authListener.remove();
            this.authListener = null;
         }

         // Clear timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         // Reset state
         this.isScanning = false;
         this.isAuthenticated = false;
         this.connectionStatus = "disconnected";
         this.connectedDevice = null;
         this.listeners = [];

         console.log("✅ Cleanup complete");
      } catch (error) {
         console.error("❌ Error during cleanup:", error);
      }
   }

   /**
    * Add event listener
    */
   addListener(callback: (event: string, data: any) => void): void {
      this.listeners.push(callback);
   }

   /**
    * Remove event listener
    */
   removeListener(callback: (event: string, data: any) => void): void {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
         this.listeners.splice(index, 1);
      }
   }

   /**
    * Notify all listeners
    */
   private notifyListeners(event: string, data: any): void {
      this.listeners.forEach((callback) => {
         try {
            callback(event, data);
         } catch (error) {
            console.error("❌ Error in listener callback:", error);
         }
      });
   }

   /**
    * Get current connection status
    */
   getConnectionStatus(): ConnectionStatus {
      return this.connectionStatus;
   }

   /**
    * Get connected device
    */
   getConnectedDevice(): IHealthDevice | null {
      return this.connectedDevice;
   }

   /**
    * Run comprehensive diagnostics for debugging
    */
   async runDiagnostics(): Promise<void> {
      console.log("🔍 === iHealth SDK Diagnostics ===");

      try {
         // Check if module is available
         console.log("📦 Module availability:", !!iHealthDeviceManagerModule);

         if (!iHealthDeviceManagerModule) {
            console.log("❌ iHealthDeviceManagerModule is not available");
            return;
         }

         // Check available methods
         console.log("🔧 Available methods:");
         const methods = [
            "sdkAuthWithLicense",
            "startDiscovery",
            "stopDiscovery",
            "connectDevice",
            "disconnectDevice",
         ];

         methods.forEach((method) => {
            const available =
               typeof iHealthDeviceManagerModule[method] === "function";
            console.log(`  - ${method}: ${available ? "✅" : "❌"}`);
         });

         // Check available constants
         console.log("🔢 Available constants:");
         const allKeys = Object.keys(iHealthDeviceManagerModule);
         const constants = allKeys.filter((key) => {
            const value = iHealthDeviceManagerModule[key];
            return typeof value === "string" || typeof value === "number";
         });

         constants.forEach((constant) => {
            console.log(
               `  - ${constant}: ${iHealthDeviceManagerModule[constant]}`,
            );
         });

         // Check for PO3 related constants
         const po3Constants = allKeys.filter(
            (key) =>
               key.toLowerCase().includes("po3") ||
               key.toLowerCase().includes("pulse") ||
               key.toLowerCase().includes("oximeter"),
         );

         if (po3Constants.length > 0) {
            console.log("🫁 PO3/Pulse Oximeter related constants:");
            po3Constants.forEach((constant) => {
               console.log(
                  `  - ${constant}: ${iHealthDeviceManagerModule[constant]}`,
               );
            });
         } else {
            console.log("⚠️ No PO3/Pulse Oximeter constants found");
         }

         // Test authentication status
         console.log("🔐 Testing authentication...");
         const authResult = await this.authenticate();
         console.log(
            `🔐 Authentication result: ${authResult ? "✅ Success" : "❌ Failed"}`,
         );
      } catch (error) {
         console.error("❌ Diagnostics failed:", error);
      }

      console.log("🔍 === End Diagnostics ===");
   }
}

export default IHealthService.getInstance();
