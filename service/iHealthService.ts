import { DeviceEventEmitter, Platform } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";

export interface IHealthDevice {
   mac: string;
   name: string;
   type: string;
   rssi?: number;
}

export interface IHealthMeasurement {
   deviceMac: string;
   timestamp: Date;
   data: any;
}

type ConnectionStatus = "disconnected" | "connecting" | "connected" | "error";

export class IHealthService {
   private static instance: IHealthService;
   private deviceEventListeners: any[] = [];
   private discoveryTimeout: NodeJS.Timeout | null = null;
   private connectedDevice: IHealthDevice | null = null;
   private connectionStatus: ConnectionStatus = "disconnected";
   private listeners: Array<(event: string, data: any) => void> = [];

   private constructor() {
      this.setupEventListeners();
   }

   static getInstance(): IHealthService {
      if (!IHealthService.instance) {
         IHealthService.instance = new IHealthService();
      }
      return IHealthService.instance;
   }

   /**
    * Authenticate with iHealth SDK using license file
    * Based on official iHealth documentation
    */
   async authenticate(): Promise<boolean> {
      try {
         console.log("🔐 Authenticating with iHealth SDK...");

         if (!iHealthDeviceManagerModule) {
            throw new Error("iHealth module not available");
         }

         // According to official docs, license file should be named "license.pem"
         // and placed in iOS project root or Android assets folder
         const filename = "license.pem";

         console.log(`🔑 Authenticating with license file: ${filename}`);
         console.log("📋 Make sure license.pem is in your project:");
         console.log("  - iOS: Added to Xcode project bundle");
         console.log("  - Android: In android/app/src/main/assets/");

         // Call authentication method as per official documentation
         if (typeof iHealthDeviceManagerModule.sdkAuthWithLicense === "function") {
            await iHealthDeviceManagerModule.sdkAuthWithLicense(filename);
            console.log("✅ Authentication successful!");
            return true;
         } else {
            throw new Error("sdkAuthWithLicense method not available");
         }
      } catch (error) {
         console.error("❌ Authentication failed:", error);
         console.error("💡 Please ensure:");
         console.error("  1. You have a valid license.pem file from dev.ihealthlabs.com");
         console.error("  2. The file is properly added to your project");
         console.error("  3. Your app bundle ID matches the license");
         return false;
      }
   }

   /**
    * Setup event listeners for device discovery and connection
    */
   private setupEventListeners(): void {
      try {
         console.log("🎧 Setting up iHealth event listeners...");

         // Device discovery events
         const discoveryListener = DeviceEventEmitter.addListener(
            "Event_Scan_Device",
            (deviceInfo: any) => {
               console.log("📱 Device discovered:", deviceInfo);
               this.notifyListeners("deviceDiscovered", deviceInfo);
            }
         );
         this.deviceEventListeners.push(discoveryListener);

         // Device connection events
         const connectionListener = DeviceEventEmitter.addListener(
            "Event_Connect_Device",
            (deviceInfo: any) => {
               console.log("🔗 Device connected:", deviceInfo);
               this.connectionStatus = "connected";
               this.connectedDevice = deviceInfo;
               this.notifyListeners("deviceConnected", deviceInfo);
            }
         );
         this.deviceEventListeners.push(connectionListener);

         // Device disconnection events
         const disconnectionListener = DeviceEventEmitter.addListener(
            "Event_Disconnect_Device",
            (deviceInfo: any) => {
               console.log("🔌 Device disconnected:", deviceInfo);
               this.connectionStatus = "disconnected";
               this.connectedDevice = null;
               this.notifyListeners("deviceDisconnected", deviceInfo);
            }
         );
         this.deviceEventListeners.push(disconnectionListener);

         console.log("✅ Event listeners setup complete");
      } catch (error) {
         console.error("❌ Failed to setup event listeners:", error);
      }
   }

   /**
    * Start scanning for PO3 pulse oximeter devices
    * Based on official iHealth documentation
    */
   async startScan(): Promise<void> {
      try {
         if (!iHealthDeviceManagerModule) {
            throw new Error("iHealth module not available");
         }

         // Clear any existing timeout
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         console.log("🔍 Starting iHealth PO3 pulse oximeter discovery...");
         console.log("🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!");
         console.log("🫁 TURN ON YOUR PO3M PULSE OXIMETER NOW!");
         
         // Stop any existing discovery first
         try {
            if (iHealthDeviceManagerModule.stopDiscovery) {
               iHealthDeviceManagerModule.stopDiscovery();
               console.log("🛑 Previous discovery stopped");
            }
         } catch (error) {
            console.log("ℹ️ No previous discovery to stop");
         }
         
         // Use "PO3" as device type (covers both PO3 and PO3M devices per official docs)
         const deviceType = "PO3";
         console.log(`📡 Starting discovery for device type: ${deviceType}`);
         
         if (typeof iHealthDeviceManagerModule.startDiscovery === "function") {
            iHealthDeviceManagerModule.startDiscovery(deviceType);
            console.log("✅ Discovery started successfully");
            console.log("🫁 Please turn on your PO3M pulse oximeter and wait for discovery...");
            
            // Set timeout for discovery
            this.discoveryTimeout = setTimeout(() => {
               console.log("⏰ Discovery timeout reached - stopping scan");
               this.stopScan();
            }, 30000); // 30 seconds timeout
         } else {
            throw new Error("startDiscovery method not available");
         }
      } catch (error) {
         console.error("❌ Failed to start PO3 discovery:", error);
         this.connectionStatus = "error";
         throw error;
      }
   }

   /**
    * Stop device discovery
    */
   async stopScan(): Promise<void> {
      try {
         if (this.discoveryTimeout) {
            clearTimeout(this.discoveryTimeout);
            this.discoveryTimeout = null;
         }

         if (iHealthDeviceManagerModule?.stopDiscovery) {
            iHealthDeviceManagerModule.stopDiscovery();
            console.log("🛑 Device discovery stopped");
         }
      } catch (error) {
         console.error("❌ Failed to stop discovery:", error);
      }
   }

   /**
    * Connect to a discovered device
    */
   async connectDevice(mac: string, deviceType: string = "PO3"): Promise<void> {
      try {
         console.log(`🔗 Connecting to device: ${mac} (${deviceType})`);
         this.connectionStatus = "connecting";
         
         if (iHealthDeviceManagerModule?.connectDevice) {
            iHealthDeviceManagerModule.connectDevice(mac, deviceType);
            console.log("🔗 Connection initiated");
         } else {
            throw new Error("connectDevice method not available");
         }
      } catch (error) {
         console.error("❌ Failed to connect device:", error);
         this.connectionStatus = "error";
         throw error;
      }
   }

   /**
    * Disconnect from current device
    */
   async disconnectDevice(): Promise<void> {
      try {
         if (this.connectedDevice && iHealthDeviceManagerModule?.disconnectDevice) {
            iHealthDeviceManagerModule.disconnectDevice(this.connectedDevice.mac, this.connectedDevice.type);
            console.log("🔌 Device disconnection initiated");
         }
      } catch (error) {
         console.error("❌ Failed to disconnect device:", error);
      }
   }

   /**
    * Add event listener
    */
   addListener(callback: (event: string, data: any) => void): void {
      this.listeners.push(callback);
   }

   /**
    * Remove event listener
    */
   removeListener(callback: (event: string, data: any) => void): void {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
         this.listeners.splice(index, 1);
      }
   }

   /**
    * Notify all listeners
    */
   private notifyListeners(event: string, data: any): void {
      this.listeners.forEach(callback => {
         try {
            callback(event, data);
         } catch (error) {
            console.error("❌ Error in listener callback:", error);
         }
      });
   }

   /**
    * Get current connection status
    */
   getConnectionStatus(): ConnectionStatus {
      return this.connectionStatus;
   }

   /**
    * Get connected device
    */
   getConnectedDevice(): IHealthDevice | null {
      return this.connectedDevice;
   }

   /**
    * Run comprehensive diagnostics for debugging
    */
   async runDiagnostics(): Promise<void> {
      console.log("🔍 === iHealth SDK Diagnostics ===");

      try {
         // Check if module is available
         console.log("📦 Module availability:", !!iHealthDeviceManagerModule);

         if (!iHealthDeviceManagerModule) {
            console.log("❌ iHealthDeviceManagerModule is not available");
            return;
         }

         // Check available methods
         console.log("🔧 Available methods:");
         const methods = [
            "sdkAuthWithLicense",
            "startDiscovery",
            "stopDiscovery",
            "connectDevice",
            "disconnectDevice"
         ];

         methods.forEach(method => {
            const available = typeof iHealthDeviceManagerModule[method] === "function";
            console.log(`  - ${method}: ${available ? "✅" : "❌"}`);
         });

         // Check available constants
         console.log("🔢 Available constants:");
         const allKeys = Object.keys(iHealthDeviceManagerModule);
         const constants = allKeys.filter(key => {
            const value = iHealthDeviceManagerModule[key];
            return typeof value === "string" || typeof value === "number";
         });

         constants.forEach(constant => {
            console.log(`  - ${constant}: ${iHealthDeviceManagerModule[constant]}`);
         });

         // Check for PO3 related constants
         const po3Constants = allKeys.filter(key =>
            key.toLowerCase().includes("po3") ||
            key.toLowerCase().includes("pulse") ||
            key.toLowerCase().includes("oximeter")
         );

         if (po3Constants.length > 0) {
            console.log("🫁 PO3/Pulse Oximeter related constants:");
            po3Constants.forEach(constant => {
               console.log(`  - ${constant}: ${iHealthDeviceManagerModule[constant]}`);
            });
         } else {
            console.log("⚠️ No PO3/Pulse Oximeter constants found");
         }

         // Test authentication status
         console.log("🔐 Testing authentication...");
         const authResult = await this.authenticate();
         console.log(`🔐 Authentication result: ${authResult ? "✅ Success" : "❌ Failed"}`);

      } catch (error) {
         console.error("❌ Diagnostics failed:", error);
      }

      console.log("🔍 === End Diagnostics ===");
   }

   /**
    * Cleanup resources
    */
   cleanup(): void {
      this.deviceEventListeners.forEach(listener => {
         if (listener?.remove) {
            listener.remove();
         }
      });
      this.deviceEventListeners = [];

      if (this.discoveryTimeout) {
         clearTimeout(this.discoveryTimeout);
         this.discoveryTimeout = null;
      }
   }
}

export default IHealthService.getInstance();
