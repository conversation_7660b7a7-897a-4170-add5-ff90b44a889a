import React, { useEffect, useState } from "react";
import { Alert, Pressable, Platform, PermissionsAndroid } from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useIHealth } from "@/hooks/useIHealth";
import iHealthService from "@/service/iHealthService";

export default function HomeScreen() {
   const { isSDKAvailable } = useIHealth();
   const [isScanning, setIsScanning] = useState(false);

   // Enhanced log function with console output
   const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${message}`;
      console.log("📱 iHealth App:", logMessage); // This will appear in terminal
   };

   useEffect(() => {
      if (!isSDKAvailable) {
         addLog("❌ iHealth SDK not available");
         return;
      }

      addLog("✅ iHealth SDK available - Running diagnostics...");

      // Run comprehensive diagnostics
      const runDiagnostics = async () => {
         try {
            await iHealthService.runDiagnostics();
            addLog("🔍 Diagnostics completed - check console for details");
         } catch (error) {
            addLog(`❌ Diagnostics failed: ${error}`);
         }
      };

      runDiagnostics();
      addLog("⚠️  IMPORTANT: Close iHealth MyVitals app if it's running!");

      // Setup basic event listeners
      const setupListeners = () => {
         iHealthService.addListener((event: string, data: any) => {
            switch (event) {
               case "deviceDiscovered":
                  addLog(
                     `🔍 Device discovered: ${data.name || "Unknown"} (${data.mac})`,
                  );
                  addLog(`📋 Device type: ${data.type}`);
                  break;
               case "deviceConnected":
                  addLog(`✅ Successfully connected to: ${data.name || data.mac}`);
                  setIsScanning(false);
                  break;
               case "deviceDisconnected":
                  addLog(`🔌 Device disconnected: ${data.name || data.mac}`);
                  setIsScanning(false);
                  break;
               case "measurementReceived":
                  addLog(`📏 Measurement received: ${JSON.stringify(data)}`);
                  Alert.alert(
                     "Measurement Received!",
                     `Data: ${JSON.stringify(data)}`,
                     [{ text: "OK" }],
                  );
                  break;
               default:
                  addLog(`📡 iHealth event: ${event} - ${JSON.stringify(data)}`);
                  break;
            }
         });
      };

      setupListeners();
      addLog("🎧 Event listeners configured");

      return () => {
         iHealthService.cleanup();
      };
   }, [isSDKAvailable]);

   const handleStartScan = async () => {
      try {
         addLog(
            "🚨 CRITICAL: Make sure iHealth MyVitals app is COMPLETELY CLOSED!",
         );
         addLog("📍 Requesting Bluetooth permissions...");

         if (Platform.OS === "android") {
            // For Android 12+ (API 31+)
            if (Platform.Version >= 31) {
               const permissions = await PermissionsAndroid.requestMultiple([
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               ]);

               if (
                  permissions[PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN] !==
                     PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[
                     PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
                  ] !== PermissionsAndroid.RESULTS.GRANTED ||
                  permissions[
                     PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
                  ] !== PermissionsAndroid.RESULTS.GRANTED
               ) {
                  addLog(
                     "❌ Required Bluetooth & Location permissions denied.",
                  );
                  Alert.alert(
                     "Permission Required",
                     "Bluetooth and Location permissions are required for device discovery.",
                  );
                  return;
               }
            } else {
               // For Android 11 and below
               const result = await PermissionsAndroid.request(
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
               );
               if (result !== PermissionsAndroid.RESULTS.GRANTED) {
                  addLog("❌ Location permission denied");
                  Alert.alert(
                     "Permission Required",
                     "Location permission is required for device discovery.",
                  );
                  return;
               }
            }
            addLog("✅ Android permissions granted.");
         } else {
            addLog(
               "✅ iOS - Bluetooth permissions should be handled automatically.",
            );
         }

         // Start scanning
         setIsScanning(true);
         addLog("🎯 Starting iHealth device discovery...");
         addLog("🫁 TURN ON YOUR PO3M PULSE OXIMETER NOW!");

         // First authenticate with iHealth SDK
         addLog("🔐 Authenticating with iHealth SDK...");
         const authResult = await iHealthService.authenticate();
         if (!authResult) {
            addLog("❌ Authentication failed - cannot start scan");
            setIsScanning(false);
            return;
         }
         addLog("✅ Authentication successful");

         // Start device discovery
         await iHealthService.startScan();
         addLog("✅ Device discovery started successfully");

         // Auto-stop scanning after 60 seconds
         setTimeout(() => {
            if (isScanning) {
               handleStopScan();
               addLog("⏰ Scan timeout - stopping scan");
            }
         }, 60000);
      } catch (error) {
         console.error("❌ Scan error details:", error);
         addLog(`❌ Scan failed: ${error}`);
         Alert.alert("Scan Error", `Failed to start scan: ${error}`);
         setIsScanning(false);
      }
   };

   const handleStopScan = async () => {
      try {
         setIsScanning(false);
         await iHealthService.stopScan();
         addLog("🛑 Scan stopped manually");
      } catch (error) {
         addLog(`❌ Stop scan failed: ${error}`);
      }
   };

   if (!isSDKAvailable) {
      return (
         <ThemedView
            style={{
               flex: 1,
               justifyContent: "center",
               alignItems: "center",
               padding: 20,
            }}
         >
            <ThemedText
               style={{ fontSize: 18, marginBottom: 20, textAlign: "center" }}
            >
               ❌ iHealth SDK Not Available
            </ThemedText>
            <ThemedText style={{ textAlign: "center", color: "#666" }}>
               The iHealth library is not properly installed. Please check the
               installation.
            </ThemedText>
         </ThemedView>
      );
   }

   return (
      <ThemedView
         style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            padding: 20,
         }}
      >
         {/* Single Scan Button */}
         <Pressable
            style={{
               backgroundColor: isScanning ? "#dc3545" : "#007bff",
               padding: 20,
               borderRadius: 10,
               marginBottom: 20,
               minWidth: 250,
               shadowColor: "#000",
               shadowOffset: { width: 0, height: 2 },
               shadowOpacity: 0.25,
               shadowRadius: 3.84,
               elevation: 5,
            }}
            onPress={isScanning ? handleStopScan : handleStartScan}
         >
            <ThemedText
               style={{
                  color: "white",
                  textAlign: "center",
                  fontSize: 18,
                  fontWeight: "bold",
               }}
            >
               {isScanning ? "STOP SCANNING" : "START DEVICE SCAN"}
            </ThemedText>
         </Pressable>
      </ThemedView>
   );
}
